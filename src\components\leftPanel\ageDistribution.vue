<!-- 游客年龄分布与年度接待游客比 -->
<template>
  <CPanel class="tourist-analysis">
    <template #header>地源热泵系统</template>
    <template #content>
      <div class="tourist-content">
        <!-- 上半部分：数据指标 -->
        <div class="data-indicators">
          <div class="indicator-item">
            <div class="icon-container">
              <div class="icon-circle">
                <img src="@/assets/img/dy_icon.png" alt="icon" class="icon-img" />
              </div>
            </div>
            <div class="indicator-content">
              <span class="indicator-label">地源水温度1</span>
              <span class="indicator-value">20.5℃</span>
            </div>
          </div>
          <div class="indicator-item">
            <div class="icon-container">
              <div class="icon-circle">
                <img src="@/assets/img/dy_icon.png" alt="icon" class="icon-img" />
              </div>
            </div>
            <div class="indicator-content">
              <span class="indicator-label">地源水温度2</span>
              <span class="indicator-value">20.5℃</span>
            </div>
          </div>
        </div>

        <!-- 下半部分：图表区域 -->
        <div class="charts-section">
          <!-- 年龄分布图表 -->
          <div class="chart-container">
            <div class="chart-title">
              <span class="title-text">换热效果趋势</span>
            </div>
            <div class="chart-wrapper">
              <CEcharts ref="ageChartRef" :option="ageOption" />
            </div>
          </div>

          <!-- 年度接待游客比图表 -->
          <div class="chart-container">
            <div class="chart-title">
              <span class="title-text">电流电压统计</span>
            </div>
            <div class="chart-wrapper">
              <CEcharts ref="receptionChartRef" :option="receptionOption" />
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'
import type { EChartsOption, TooltipComponentOption } from 'echarts'

const ageOption = ref<EChartsOption>({})
const receptionOption = ref<any>({})
const ageChartRef = ref()
const receptionChartRef = ref()
// 换热效果趋势图表数据
const heatEffectData: number[] = [18, 25, 32, 38, 45, 52, 58, 65, 72, 75, 78, 76]

const createHeatEffectChart = (): EChartsOption => {

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line'
      },
      formatter: function (params: any) {
        const item = params[0]
        return item.name + ' : ' + item.value + ' m³/h'
      }
    } as TooltipComponentOption,
    grid: {
      left: '8%',
      right: '5%',
      top: '15%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['7.1', '7.2', '7.3', '7.4', '7.5', '7.6', '7.7', '7.8', '7.9'],
      axisLine: {
        show: true,
        lineStyle: {
          width: 1,
          color: 'rgba(76, 93, 130, 1)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontSize: 12,
        color: 'rgba(201, 211, 234, 1)'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      name: 'm³/h',
      nameTextStyle: {
        color: 'rgba(201, 211, 234, 1)',
        fontSize: 12,
        padding: [0, 20, 8, 0]
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(49, 58, 86, 1)',
          type: 'solid'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontSize: 12,
        color: 'rgba(201, 211, 234, 1)'
      },
      min: 0,
      max: 100,
      interval: 20
    },
    series: [
      {
        type: 'line',
        data: heatEffectData,
        lineStyle: {
          width: 3,
          color: 'rgba(64, 158, 255, 1)'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(64, 158, 255, 0.6)'
            },
            {
              offset: 1,
              color: 'rgba(64, 158, 255, 0.1)'
            }
          ])
        },
        itemStyle: {
          color: 'rgba(64, 158, 255, 1)',
          borderColor: 'rgba(64, 158, 255, 1)',
          borderWidth: 2
        },
        symbol: 'circle',
        symbolSize: 6,
        smooth: true,
        emphasis: {
          itemStyle: {
            color: 'rgba(64, 158, 255, 1)',
            borderColor: '#fff',
            borderWidth: 3,
            shadowBlur: 10,
            shadowColor: 'rgba(64, 158, 255, 0.5)'
          }
        }
      }
    ]
  }
}
// 创建年度接待游客比图表
const createReceptionChart = () => {
  return {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      top: '5%',
      right: '2%',
      itemGap: 20,
      itemWidth: 15,
      itemHeight: 1,
      textStyle: {
        color: '#C5D6E6',
        fontSize: 10
      }
    },
    grid: {
      left: '8%',
      right: '5%',
      bottom: '15%',
      top: '25%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        axisLabel: {
          textStyle: {
            color: '#C5D6E6',
            fontSize: 10
          }
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(76, 93, 130, 1)'
          }
        },
        axisTick: {
          show: false
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '万人',
        nameTextStyle: {
          color: 'rgba(201, 211, 234, 1)',
          fontSize: 10,
          padding: [0, 20, 8, 0]
        },
        splitNumber: 3,
        splitLine: {
          lineStyle: {
            color: 'rgba(52, 71, 112, 1)',
            type: 'dashed'
          }
        },
        axisLabel: {
          textStyle: {
            color: '#C5D6E6',
            fontSize: 10
          }
        },
        axisLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '2021年',
        type: 'line',
        data: [23, 60, 20, 36, 23, 85, 70, 60, 78, 89, 68, 56],
        lineStyle: {
          normal: {
            width: 2,
            color: 'rgba(218, 163, 88, 1)',
            shadowColor: 'rgba(218, 163, 88, 0.3)',
            shadowBlur: 10,
            shadowOffsetY: 20
          }
        },
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(218, 163, 88, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(218, 163, 88, 0)'
                }
              ],
              false
            )
          }
        },
        itemStyle: {
          color: 'rgba(15, 222, 255, 1)'
        },
        smooth: true,
        symbol: 'none'
      },
      {
        name: '2022年',
        type: 'line',
        data: [145, 78, 88, 99, 36, 109, 120, 150, 99, 89, 100, 120],
        lineStyle: {
          normal: {
            width: 2,
            color: 'rgba(109, 128, 175, 1)'
          }
        },
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(109, 128, 175, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(109, 128, 175, 0)'
                }
              ],
              false
            )
          }
        },
        itemStyle: {
          color: 'rgba(109, 128, 175, 1)'
        },
        smooth: true,
        symbol: 'none'
      }
    ]
  }
}

onMounted(() => {
  ageOption.value = createHeatEffectChart()
  receptionOption.value = createReceptionChart()
})
onUnmounted(() => {
  // 清理资源
})
</script>

<style lang="scss" scoped>
.tourist-analysis {
  .tourist-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
    background: url('@/assets/img/dy_bg.png') no-repeat center center;
    background-size: 100% 100%;
  }

  .data-indicators {
    display: flex;
    gap: 16px;
    height: 80px;

    .indicator-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      padding: 12px 16px;
      .icon-container {
        .icon-circle {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
        }
      }

      .indicator-content {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .indicator-label {
          font-size: 12px;
          color: rgba(201, 211, 234, 0.8);
          line-height: 1;
        }

        .indicator-value {
          font-size: 16px;
          font-weight: bold;
          color: #D9F0FF;
          line-height: 1;
          background: url('@/assets/img/dy_title.png') no-repeat center center;
          background-size: contain;
          padding: 4px 8px;
        }
      }
    }
  }

  .charts-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .chart-container {
      flex: 1;
      display: flex;
      flex-direction: column;

      .chart-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        background: url('@/assets/img/n_samllTitle.png') no-repeat center center;
        background-size: contain;
        padding: 5px 24px;
        .title-text {
          font-size: 14px;
          color: rgba(201, 211, 234, 1);
          font-weight: 500;
        }
      }

      .chart-wrapper {
        flex: 1;
        min-height: 120px;
      }
    }
  }
}
</style>
